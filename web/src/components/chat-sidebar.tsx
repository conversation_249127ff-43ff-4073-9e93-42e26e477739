"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageSquare, Plus, Trash2 } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import { toast } from "sonner";

interface ChatHistory {
  id: string;
  title: string;
  timestamp: Date;
  messageCount: number;
}

interface ChatSidebarProps {
  currentChatId?: string;
  className?: string;
  selectedAgent?: string;
}

export function ChatSidebar({ currentChatId, className = "", selectedAgent = "simpleChat" }: ChatSidebarProps) {
  const router = useRouter();
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);

  // 加载聊天历史
  useEffect(() => {
    loadChatHistory();
  }, []);

  const loadChatHistory = () => {
    const history: ChatHistory[] = [];
    
    // 从localStorage获取所有聊天记录
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('chat-messages-')) {
        const chatId = key.replace('chat-messages-', '');
        try {
          const messages = JSON.parse(localStorage.getItem(key) || '[]');
          if (messages.length > 0) {
            const firstMessage = messages[0];
            const title = firstMessage?.content?.slice(0, 30) + (firstMessage?.content?.length > 30 ? '...' : '') || '新对话';
            history.push({
              id: chatId,
              title,
              timestamp: new Date(firstMessage?.timestamp || Date.now()),
              messageCount: messages.length
            });
          }
        } catch (error) {
          console.error('Failed to parse chat history:', error);
        }
      }
    }
    
    // 按时间排序，最新的在前面
    history.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    setChatHistory(history);
  };

  const createNewChat = () => {
    const chatId = uuidv4();
    router.push(`/chat/${chatId}?agent=${selectedAgent}`);
  };

  const openChat = (chatId: string) => {
    router.push(`/chat/${chatId}`);
  };

  const deleteChat = (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    localStorage.removeItem(`chat-messages-${chatId}`);
    loadChatHistory();
    toast.success("聊天记录已删除");
    
    // 如果删除的是当前聊天，跳转到首页
    if (chatId === currentChatId) {
      router.push("/");
    }
  };

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">聊天记录</CardTitle>
        <Button onClick={createNewChat} className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          新建对话
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-2 p-4">
            {chatHistory.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">暂无聊天记录</p>
              </div>
            ) : (
              chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  className={`p-3 rounded-lg border cursor-pointer hover:bg-accent transition-colors ${
                    chat.id === currentChatId ? 'bg-accent border-primary' : 'hover:border-primary/50'
                  }`}
                  onClick={() => openChat(chat.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{chat.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {chat.messageCount} 条消息
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {chat.timestamp.toLocaleDateString()} {chat.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-1 opacity-60 hover:opacity-100"
                      onClick={(e) => deleteChat(chat.id, e)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
