"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ThemeToggle } from "@/components/theme-toggle";
import { Bot, MessageSquare, Zap, Plus, Settings } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import { AVAILABLE_AGENTS } from "@/common/constants";

export default function HomePage() {
  const router = useRouter();
  const [selectedAgent, setSelectedAgent] = useState<string>("simpleChat");

  const handleStartChat = () => {
    const chatId = uuidv4();
    // 将选择的agent作为查询参数传递
    router.push(`/chat/${chatId}?agent=${selectedAgent}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-foreground mb-2">
              LangGraph MVP Demo
            </h1>
            <p className="text-muted-foreground text-lg">
              基于LangGraph的智能Agent服务平台
            </p>
          </div>
          <ThemeToggle />
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <Bot className="h-6 w-6 text-primary mr-2" />
              <CardTitle className="text-lg">智能Agent</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                基于LangGraph构建的多智能体系统，支持复杂任务处理
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <Zap className="h-6 w-6 text-primary mr-2" />
              <CardTitle className="text-lg">流式处理</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                实时流式数据处理，即时响应用户请求
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <MessageSquare className="h-6 w-6 text-primary mr-2" />
              <CardTitle className="text-lg">交互式界面</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                现代化的用户界面，支持多种交互方式
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Agent选择区域 */}
        <div className="max-w-md mx-auto mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-center flex items-center justify-center gap-2">
                <Settings className="h-5 w-5" />
                选择智能助手
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">助手类型</label>
                <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择一个助手" />
                  </SelectTrigger>
                  <SelectContent>
                    {AVAILABLE_AGENTS.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        <div className="flex items-center gap-2">
                          <agent.icon className="h-4 w-4" />
                          {agent.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 显示选中agent的描述 */}
              {selectedAgent && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm text-muted-foreground">
                    {AVAILABLE_AGENTS.find(agent => agent.id === selectedAgent)?.description}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="text-center space-y-4">
          <Button
            onClick={handleStartChat}
            size="lg"
            className="px-8 py-3 text-lg"
          >
            <Plus className="h-5 w-5 mr-2" />
            开始新对话
          </Button>
          <p className="text-sm text-muted-foreground">
            每次对话都会生成唯一的聊天ID，您可以随时返回继续对话
          </p>
        </div>

        <div className="mt-12 text-center text-muted-foreground">
          <p>支持的Agent服务：designToCode</p>
          <p className="text-sm mt-2">服务地址：http://localhost:2024</p>
        </div>
      </div>
    </div>
  );
}
