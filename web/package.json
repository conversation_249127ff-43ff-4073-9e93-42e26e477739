{"name": "langgraph-mvp-web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@langchain/core": "^0.3.72", "@langchain/langgraph-sdk": "^0.0.95", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/postcss": "^4.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.9", "langgraph-nextjs-api-passthrough": "^0.1.4", "lucide-react": "^0.532.0", "next": "^15.2.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.0.1", "react-syntax-highlighter": "^15.5.0", "sonner": "^2.0.1", "swr": "^2.3.4", "tailwind-merge": "^3.0.2", "tailwind-scrollbar": "^4.0.2", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^22.13.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-config-next": "15.2.2", "next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.13", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.2"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}