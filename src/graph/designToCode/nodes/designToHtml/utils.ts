export const isHtml = (content: string) => {
  return (
    content &&
    (content.includes("<html") ||
      content.includes("<!DOCTYPE") ||
      content.includes("<div") ||
      content.includes("<body"))
  );
};

export const isImg = (content: string) => {
  if (!content) return false;

  // 检查图片URL
  if (
    content.includes("http") &&
    (content.includes(".jpg") ||
      content.includes(".jpeg") ||
      content.includes(".png") ||
      content.includes(".gif") ||
      content.includes(".webp"))
  ) {
    return true;
  }

  // 检查base64图片数据
  if (content.includes("data:image")) {
    return true;
  }

  return false;
};
